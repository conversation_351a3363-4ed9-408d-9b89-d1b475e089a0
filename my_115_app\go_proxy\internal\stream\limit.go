package stream

import (
	"context"
	"io"

	"go_proxy/internal/model"
	"go_proxy/pkg/http_range"
	"go_proxy/pkg/utils"
)

type RateLimitReader struct {
	io.Reader
	Ctx context.Context
}

func (r *RateLimitReader) Read(p []byte) (n int, err error) {
	if r.Ctx != nil && utils.IsCanceled(r.Ctx) {
		return 0, r.Ctx.Err()
	}
	n, err = r.Reader.Read(p)
	if err != nil {
		return
	}
	return
}

func (r *RateLimitReader) Close() error {
	if c, ok := r.Reader.(io.Closer); ok {
		return c.Close()
	}
	return nil
}

type RateLimitWriter struct {
	io.Writer
	Ctx context.Context
}

func (w *RateLimitWriter) Write(p []byte) (n int, err error) {
	if w.Ctx != nil && utils.IsCanceled(w.Ctx) {
		return 0, w.Ctx.Err()
	}
	n, err = w.Writer.Write(p)
	if err != nil {
		return
	}
	return
}

func (w *RateLimitWriter) Close() error {
	if c, ok := w.Writer.(io.Closer); ok {
		return c.Close()
	}
	return nil
}

type RateLimitRangeReadCloser struct {
	model.RangeReadCloserIF
}

func (rrc *RateLimitRangeReadCloser) RangeRead(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error) {
	rc, err := rrc.RangeReadCloserIF.RangeRead(ctx, httpRange)
	if err != nil {
		return nil, err
	}
	return &RateLimitReader{
		Reader: rc,
		Ctx:    ctx,
	}, nil
}
