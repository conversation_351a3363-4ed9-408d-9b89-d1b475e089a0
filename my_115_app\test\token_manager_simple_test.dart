import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';

import 'package:my_115_app/services/talker_service.dart';
import 'package:my_115_app/services/token_manager.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('TokenManager Core Logic Tests', () {
    late TokenManager tokenManager;

    setUp(() async {
      await GetIt.instance.reset();
      
      // 注册服务
      final talkerService = TalkerService();
      talkerService.initialize();
      GetIt.instance.registerSingleton<TalkerService>(talkerService);
      GetIt.instance.registerSingleton<TokenManager>(TokenManager());
      
      tokenManager = GetIt.instance<TokenManager>();
    });

    tearDown(() async {
      await GetIt.instance.reset();
    });

    test('应该能够检测需要刷新的错误码', () {
      final testCases = [
        {'code': 40140123, 'expected': true},
        {'code': 40140124, 'expected': true},
        {'code': 40140125, 'expected': true},
        {'code': 40140126, 'expected': true},
        {'code': 0, 'expected': false},
        {'code': 40140127, 'expected': false},
        {'code': 500, 'expected': false},
        {'code': null, 'expected': false},
      ];

      for (final testCase in testCases) {
        final responseData = {'code': testCase['code']};
        final needsRefresh = tokenManager.needsTokenRefresh(responseData);
        expect(needsRefresh, equals(testCase['expected']),
            reason: 'Code ${testCase['code']} should ${testCase['expected'] == true ? '' : 'not '}trigger refresh');
      }
    });

    test('应该正确处理空的响应数据', () {
      expect(tokenManager.needsTokenRefresh({}), isFalse);
      expect(tokenManager.needsTokenRefresh({'message': 'error'}), isFalse);
      expect(tokenManager.needsTokenRefresh({'code': 'invalid'}), isFalse);
    });

    test('应该正确处理边界值', () {
      final testCases = [
        {'code': 40140122, 'expected': false}, // 一个小于范围的值
        {'code': 40140127, 'expected': false}, // 一个大于范围的值
        {'code': -1, 'expected': false},       // 负数
        {'code': 0, 'expected': false},        // 零
      ];

      for (final testCase in testCases) {
        final responseData = {'code': testCase['code']};
        final needsRefresh = tokenManager.needsTokenRefresh(responseData);
        expect(needsRefresh, equals(testCase['expected']),
            reason: 'Boundary code ${testCase['code']} should ${testCase['expected'] == true ? '' : 'not '}trigger refresh');
      }
    });
  });

  group('TokenManager Error Handling Tests', () {
    late TokenManager tokenManager;

    setUp(() async {
      await GetIt.instance.reset();
      
      final talkerService = TalkerService();
      talkerService.initialize();
      GetIt.instance.registerSingleton<TalkerService>(talkerService);
      GetIt.instance.registerSingleton<TokenManager>(TokenManager());
      
      tokenManager = GetIt.instance<TokenManager>();
    });

    tearDown(() async {
      await GetIt.instance.reset();
    });

    test('应该处理无效的响应数据类型', () {
      // 测试各种无效输入
      expect(() => tokenManager.needsTokenRefresh(null as dynamic), throwsA(isA<TypeError>()));
      
      // 测试响应数据为null的情况
      try {
        final result = tokenManager.needsTokenRefresh({'code': null});
        expect(result, isFalse);
      } catch (e) {
        // 如果抛出异常，也是可以接受的
        expect(e, isA<TypeError>());
      }
    });

    test('TokenManager应该有正确的单例行为', () {
      final instance1 = TokenManager();
      final instance2 = TokenManager();
      
      expect(identical(instance1, instance2), isTrue,
          reason: 'TokenManager应该是单例');
    });
  });

  group('Business Logic Tests', () {
    test('验证115 API错误码常量', () {
      // 这些是115 API返回的需要刷新token的错误码
      const refreshCodes = [40140123, 40140124, 40140125, 40140126];
      
      for (final code in refreshCodes) {
        final tokenManager = TokenManager();
        final responseData = {'code': code, 'message': 'Token过期'};
        expect(tokenManager.needsTokenRefresh(responseData), isTrue,
            reason: '115 API错误码 $code 应该触发token刷新');
      }
    });

    test('验证正常业务响应不会触发刷新', () {
      final tokenManager = TokenManager();
      
      // 成功响应
      expect(tokenManager.needsTokenRefresh({'code': 0, 'state': true}), isFalse);
      
      // 其他业务错误
      expect(tokenManager.needsTokenRefresh({'code': 20001, 'message': '参数错误'}), isFalse);
      expect(tokenManager.needsTokenRefresh({'code': 50000, 'message': '服务器错误'}), isFalse);
    });
  });
} 