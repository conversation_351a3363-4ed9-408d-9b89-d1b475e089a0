import 'package:talker/talker.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';
import 'package:dio/dio.dart';

/// 完整的 Talker 服务 - 基于官方 talker 包的现代日志解决方案
/// 提供日志记录、错误处理、调试界面等完整功能
class TalkerService {
  static final TalkerService _instance = TalkerService._internal();
  factory TalkerService() => _instance;
  TalkerService._internal();

  late final Talker _talker;
  late final TalkerDioLogger _dioLogger;
  bool _isInitialized = false;

  /// 获取 Talker 实例
  Talker get talker => _talker;

  /// 获取 TalkerDioLogger 实例，用于添加到 Dio 拦截器
  TalkerDioLogger get dioLogger => _dioLogger;

  /// 初始化服务
  void initialize() {
    if (_isInitialized) return;
    // 创建核心 Talker 实例
    _talker = Talker(
      settings: TalkerSettings(
        enabled: true,
        useHistory: true,
        maxHistoryItems: 1000,
        useConsoleLogs: true,
      ),
      logger: TalkerLogger(
        settings: TalkerLoggerSettings(
          enableColors: true,
          level: LogLevel.verbose,
        ),
      ),
    );

    // 创建 Dio Logger，并关联到主 Talker 实例
    _dioLogger = TalkerDioLogger(
      talker: _talker,
      settings: const TalkerDioLoggerSettings(
        printRequestHeaders: true,
        printResponseHeaders: true,
        printResponseMessage: true,
        printRequestData: true,
        printResponseData: true,
        
        // 过滤敏感路径
        requestFilter: _requestFilter,
        responseFilter: _responseFilter,
      ),
    );

    // 记录服务初始化
    _talker.info('🎯 TalkerService initialized successfully');
    _isInitialized = true;
  }

  /// 请求过滤器 - 过滤敏感信息
  static bool _requestFilter(RequestOptions options) {
    final sensitivePathsPatterns = [
      '/secure',
      '/auth/login', // 登录请求可能包含密码
      '/token',      // Token相关请求
    ];
    
    return !sensitivePathsPatterns.any((pattern) => 
        options.path.toLowerCase().contains(pattern.toLowerCase()));
  }

  /// 响应过滤器
  static bool _responseFilter(Response response) {
    return response.statusCode != 301 && response.statusCode != 302;
  }

  // ===== 基础日志方法 =====
  
  /// 记录信息日志
  void info(String message) {
    _talker.info(message);
  }

  /// 记录调试日志
  void debug(String message) {
    _talker.debug(message);
  }

  /// 记录警告日志
  void warning(String message) {
    _talker.warning(message);
  }

  /// 记录错误日志
  void error(String message, [Object? exception, StackTrace? stackTrace]) {
    _talker.error(message, exception, stackTrace);
  }

  /// 记录致命错误日志
  void critical(String message, [Object? exception, StackTrace? stackTrace]) {
    _talker.critical(message, exception, stackTrace);
  }

  /// 记录成功日志（使用 info 替代）
  void good(String message) {
    _talker.info('✅ $message');
  }

  /// 记录详细日志
  void verbose(String message) {
    _talker.verbose(message);
  }

  // ===== 错误处理方法 =====

  /// 处理异常
  void handleException(Object exception, StackTrace stackTrace, [String? message]) {
    _talker.handle(exception, stackTrace, message);
  }

  /// 记录异常
  void logException(Object exception, StackTrace stackTrace, [String? message]) {
    _talker.handle(exception, stackTrace, message);
  }

  /// 记录错误
  void logError(Object error, [StackTrace? stackTrace, String? message]) {
    if (error is Error && stackTrace != null) {
      _talker.handle(error, stackTrace, message);
    } else {
      _talker.error(message ?? error.toString());
    }
  }

  // ===== 自定义日志类型 =====

  /// 记录 API 调用开始
  void logApiStart(String method, String endpoint) {
    _talker.logCustom(ApiStartLog(method, endpoint));
  }

  /// 记录 API 调用成功
  void logApiSuccess(String method, String endpoint, int statusCode, Duration duration) {
    _talker.logCustom(ApiSuccessLog(method, endpoint, statusCode, duration));
  }

  /// 记录 API 调用失败
  void logApiError(String method, String endpoint, String error) {
    _talker.logCustom(ApiErrorLog(method, endpoint, error));
  }

  /// 记录认证事件
  void logAuth(String message) {
    _talker.logCustom(AuthLog(message));
  }

  /// 记录缓存事件
  void logCache(String message) {
    _talker.logCustom(CacheLog(message));
  }

  // ===== 数据访问方法 =====

  /// 获取所有日志历史
  List<TalkerData> getHistory() {
    return _talker.history;
  }

  /// 按类型过滤日志
  List<TalkerData> getLogsByType(Type type) {
    return _talker.history.where((log) => log.runtimeType == type).toList();
  }

  /// 搜索日志
  List<TalkerData> searchLogs(String query) {
    return _talker.history.where((log) => 
        log.displayMessage.toLowerCase().contains(query.toLowerCase())).toList();
  }

  /// 清空日志历史
  void clearHistory() {
    _talker.cleanHistory();
  }

  /// 获取日志统计信息
  Map<String, dynamic> getStats() {
    final history = _talker.history;
    final stats = <String, int>{};
    
    for (final log in history) {
      final type = log.runtimeType.toString();
      stats[type] = (stats[type] ?? 0) + 1;
    }
    
    return {
      'totalLogs': history.length,
      'byType': stats,
      'errors': history.where((log) => log is TalkerError).length,
      'exceptions': history.where((log) => log is TalkerException).length,
      'maxHistoryItems': 1000,
      'provider': 'talker',
      'version': '4.9.3',
    };
  }

  /// 导出日志为字符串
  String exportLogs() {
    return _talker.history.map((log) => log.generateTextMessage()).join('\n');
  }

  /// 销毁服务
  void dispose() {
    // Talker 不需要手动销毁
  }
}

// ===== 自定义日志类型 =====

/// API 开始调用日志
class ApiStartLog extends TalkerLog {
  final String method;
  final String endpoint;

  ApiStartLog(this.method, this.endpoint) : super('$method $endpoint');

  @override
  String get title => 'API START';

  @override
  String get key => 'api_start';

  @override
  AnsiPen get pen => AnsiPen()..blue();
}

/// API 成功调用日志
class ApiSuccessLog extends TalkerLog {
  final String method;
  final String endpoint;
  final int statusCode;
  final Duration duration;

  ApiSuccessLog(this.method, this.endpoint, this.statusCode, this.duration)
      : super('$method $endpoint -> $statusCode (${duration.inMilliseconds}ms)');

  @override
  String get title => 'API SUCCESS';

  @override
  String get key => 'api_success';

  @override
  AnsiPen get pen => AnsiPen()..green();
}

/// API 错误调用日志
class ApiErrorLog extends TalkerLog {
  final String method;
  final String endpoint;
  final String errorMessage;

  ApiErrorLog(this.method, this.endpoint, this.errorMessage)
      : super('$method $endpoint ERROR: $errorMessage');

  @override
  String get title => 'API ERROR';

  @override
  String get key => 'api_error';

  @override
  AnsiPen get pen => AnsiPen()..red();
}

/// 认证日志
class AuthLog extends TalkerLog {
  AuthLog(String message) : super(message);

  @override
  String get title => 'AUTH';

  @override
  String get key => 'auth';

  @override
  AnsiPen get pen => AnsiPen()..magenta();
}

/// 缓存日志
class CacheLog extends TalkerLog {
  CacheLog(String message) : super(message);

  @override
  String get title => 'CACHE';

  @override
  String get key => 'cache';

  @override
  AnsiPen get pen => AnsiPen()..cyan();
} 