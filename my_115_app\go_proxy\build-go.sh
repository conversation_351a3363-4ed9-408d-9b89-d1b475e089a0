#!/bin/bash

# 配置区
OUTPUT_DIR="build_output"
APP_NAME="video_proxy"
GO_MAIN="./cmd/main.go"
NDK_DOWNLOAD_URL="https://dl.google.com/android/repository/android-ndk-r27c-linux.zip"
NDK_DIR="$HOME/android-ndk-r27c"
ENABLE_UPX=${ENABLE_UPX:-false}
KEEP_ORIGINAL=true

# 创建临时和输出目录
mkdir -p "$OUTPUT_DIR"
mkdir -p temp

# 检查并安装必要的依赖
check_and_install_deps() {
    echo "检查必要的依赖工具..."
    
    # 检查是否为基于Debian的系统
    if command -v apt-get &> /dev/null; then
        PACKAGE_MANAGER="apt-get"
    # 检查是否为基于Red Hat的系统
    elif command -v yum &> /dev/null; then
        PACKAGE_MANAGER="yum"
    else
        echo "警告: 无法确定包管理器，请手动安装缺少的依赖"
        return
    fi
    
    # 检查并安装unzip
    if ! command -v unzip &> /dev/null; then
        echo "正在安装unzip..."
        sudo $PACKAGE_MANAGER update
        sudo $PACKAGE_MANAGER install -y unzip
    fi
    
    # 检查并安装wget
    if ! command -v wget &> /dev/null; then
        echo "正在安装wget..."
        sudo $PACKAGE_MANAGER update
        sudo $PACKAGE_MANAGER install -y wget
    fi
}

# 首先安装必要的依赖
check_and_install_deps

# 下载并安装NDK
if [ ! -d "$NDK_DIR" ]; then
    echo "NDK目录不存在，准备安装..."
    
    # 确保wget可用
    if ! command -v wget &> /dev/null; then
        echo "错误: 未找到wget命令，无法下载NDK"
        echo "请手动安装wget或直接下载NDK到$HOME目录并解压"
        exit 1
    fi
    
    # 检查下载文件是否已存在
    if [ ! -f "temp/android-ndk.zip" ]; then
        echo "正在下载Android NDK..."
        wget -O temp/android-ndk.zip "$NDK_DOWNLOAD_URL"
    else
        echo "发现已下载的NDK压缩包，跳过下载步骤..."
    fi
    
    echo "解压NDK..."
    # 确保unzip可用
    if ! command -v unzip &> /dev/null; then
        echo "错误: 未找到unzip命令，无法解压NDK"
        echo "请手动安装unzip或直接下载NDK到$HOME目录并解压"
        exit 1
    fi
    
    unzip -q temp/android-ndk.zip -d "$HOME"
    
    echo "清理临时文件..."
    # 保留下载的文件，下次运行时可以重用
    # rm -f temp/android-ndk.zip
    
    echo "NDK已安装到 $NDK_DIR"
fi

# 设置NDK路径
NDK_BIN="$NDK_DIR/toolchains/llvm/prebuilt/linux-x86_64/bin"

# 确认NDK已正确安装
if [ ! -d "$NDK_BIN" ]; then
    echo "错误: NDK工具链路径不存在: $NDK_BIN"
    exit 1
fi

# 可选：安装UPX（如果启用）
if [ "$ENABLE_UPX" = true ] && ! command -v upx &> /dev/null; then
    echo "安装UPX..."
    if [ "$PACKAGE_MANAGER" = "apt-get" ]; then
        sudo apt-get update
        sudo apt-get install -y upx-ucl
    elif [ "$PACKAGE_MANAGER" = "yum" ]; then
        sudo yum install -y upx
    else
        echo "警告: 无法自动安装UPX，请手动安装"
    fi
fi

# 构建目标配置
declare -A targets
targets=(
    # Android 目标
    ["android-armv7a"]="OS=android ARCH=arm GOARCH=arm GOARM=7 CC=$NDK_BIN/armv7a-linux-androideabi35-clang USE_UPX=true"
    ["android-arm64"]="OS=android ARCH=arm64 GOARCH=arm64 CC=$NDK_BIN/aarch64-linux-android35-clang USE_UPX=true"
    
    # Linux 目标
    ["linux-amd64"]="OS=linux ARCH=amd64 GOARCH=amd64 CC= USE_UPX=false"

    # Windows 目标
    ["windows-amd64"]="OS=windows ARCH=amd64 GOARCH=amd64 CC= USE_UPX=false"
    ["windows-arm64"]="OS=windows ARCH=arm64 GOARCH=arm64 CC= USE_UPX=false"

    # 安卓模拟器
    ["android-arm64-simulator"]="OS=android ARCH=arm64 GOARCH=arm64 CC=$NDK_BIN/aarch64-linux-android35-clang USE_UPX=true"
    ["android-amd64-simulator"]="OS=android ARCH=amd64 GOARCH=amd64 CC=$NDK_BIN/x86_64-linux-android35-clang USE_UPX=true"
)

# 执行构建
for target in "${!targets[@]}"; do
    # 解析环境变量
    eval "${targets[$target]}"
    
    # 生成输出文件名
    ORIGINAL_FILE="$OUTPUT_DIR/$APP_NAME-$target"
    COMPRESSED_FILE="$ORIGINAL_FILE-upx"
    
    echo "构建 $target..."
    echo "GOOS=$OS GOARCH=$GOARCH GOARM=$GOARM CGO_ENABLED=${CC:+1} CC=$CC"
    
    # 设置环境变量并执行编译
    GOOS=$OS GOARCH=$GOARCH GOARM=$GOARM CGO_ENABLED=${CC:+1} CC=$CC \
    go build -o "$ORIGINAL_FILE" -trimpath -ldflags="-s -w" "$GO_MAIN"
    
    if [ ! -f "$ORIGINAL_FILE" ]; then
        echo "✗ 编译失败: $target"
        continue
    fi
    
    # UPX压缩处理
    if [ "$ENABLE_UPX" = true ] && [ "$USE_UPX" = true ] && command -v upx &> /dev/null; then
        echo "正在压缩 $target..."
        upx --best -q -o "$COMPRESSED_FILE" "$ORIGINAL_FILE"
        
        if [ $? -eq 0 ]; then
            # 压缩成功后处理文件
            if [ "$KEEP_ORIGINAL" = false ]; then
                rm -f "$ORIGINAL_FILE"
                mv "$COMPRESSED_FILE" "$ORIGINAL_FILE"
            else
                rm -f "$ORIGINAL_FILE"
                mv "$COMPRESSED_FILE" "$ORIGINAL_FILE"
            fi
            echo "↑ 压缩成功: $(du -h "$ORIGINAL_FILE" | cut -f1)"
        else
            echo "UPX压缩失败: $target"
            # 清理残留文件
            rm -f "$COMPRESSED_FILE"
        fi
    else
        if [ "$USE_UPX" = false ]; then
            echo "跳过 $target 的UPX压缩处理..."
        fi
    fi
    
    echo "✓ 完成构建: $target"
done

echo -e "\n构建报告:"
ls -lh "$OUTPUT_DIR" | grep -v "^total" | awk '{print "✓ " $9 "\t" $5}'