import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/services.dart';

import 'package:my_115_app/services/token_manager.dart';
import 'package:my_115_app/services/talker_service.dart';

// Mock FlutterSecureStorage for testing
class MockSecureStorage extends FlutterSecureStorage {
  final Map<String, String> _storage = <String, String>{};

  @override
  Future<void> write({required String key, required String? value, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    if (value != null) {
      _storage[key] = value;
    }
  }

  @override
  Future<String?> read({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    return _storage[key];
  }

  @override
  Future<void> delete({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    _storage.remove(key);
  }

  @override
  Future<void> deleteAll({IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    _storage.clear();
  }

  // Helper method for testing
  void clear() {
    _storage.clear();
  }
}

void main() {
  // 初始化Flutter绑定用于测试
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('TokenManager Tests', () {
    late TokenManager tokenManager;
    late MockSecureStorage mockStorage;

    setUpAll(() {
      // Mock Flutter Secure Storage method channel
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          // Mock implementation - return null for all calls
          return null;
        },
      );
    });

    setUp(() async {
      // 重置GetIt实例
      await GetIt.instance.reset();
      
      // 设置mock存储
      mockStorage = MockSecureStorage();
      
      // 注册mock服务
      final talkerService = TalkerService();
      talkerService.initialize();
      GetIt.instance.registerSingleton<TalkerService>(talkerService);
      GetIt.instance.registerSingleton<TokenManager>(TokenManager());
      
      tokenManager = GetIt.instance<TokenManager>();
      
      // 清理存储
      mockStorage.clear();
    });

    tearDown(() async {
      await GetIt.instance.reset();
      mockStorage.clear();
    });

    test('应该能够保存和读取Token', () async {
      // 准备测试数据
      const accessToken = 'test_access_token';
      const refreshToken = 'test_refresh_token';
      final expiry = DateTime.now().add(const Duration(hours: 1));
      final userInfo = {'userId': '123', 'username': '<EMAIL>'};

      // 保存Token
      await tokenManager.saveTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiry: expiry,
        userInfo: userInfo,
      );

      // 验证Token是否保存成功
      final savedAccessToken = await tokenManager.getAccessToken();
      final savedRefreshToken = await tokenManager.getRefreshToken();
      final savedExpiry = await tokenManager.getTokenExpiry();
      final savedUserInfo = await tokenManager.getUserInfo();

      expect(savedAccessToken, equals(accessToken));
      expect(savedRefreshToken, equals(refreshToken));
      expect(savedExpiry?.millisecondsSinceEpoch, equals(expiry.millisecondsSinceEpoch));
      expect(savedUserInfo, equals(userInfo));
    });

    test('应该能够检查Token是否即将过期', () async {
      // 准备即将过期的Token
      final expiry = DateTime.now().add(const Duration(minutes: 3)); // 3分钟后过期
      
      await tokenManager.saveTokens(
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        expiry: expiry,
      );

      // 检查是否即将过期（5分钟阈值）
      final isExpiring = await tokenManager.isTokenExpiringSoon();
      expect(isExpiring, isTrue);
    });

    test('应该能够检查有效Token存在', () async {
      // 保存Token
      await tokenManager.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
      );

      // 检查是否有有效Token
      final hasValidTokens = await tokenManager.hasValidTokens();
      expect(hasValidTokens, isTrue);
    });

    test('应该能够清除所有Token', () async {
      // 保存Token
      await tokenManager.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        userInfo: {'userId': '123'},
      );

      // 验证Token存在
      expect(await tokenManager.hasValidTokens(), isTrue);

      // 清除Token
      await tokenManager.clearTokens();

      // 验证Token已清除
      expect(await tokenManager.hasValidTokens(), isFalse);
      expect(await tokenManager.getAccessToken(), isNull);
      expect(await tokenManager.getRefreshToken(), isNull);
      expect(await tokenManager.getUserInfo(), isNull);
    });

    test('应该能够检测需要刷新的错误码', () {
      final testCases = [
        {'code': 40140123, 'expected': true},
        {'code': 40140124, 'expected': true},
        {'code': 40140125, 'expected': true},
        {'code': 40140126, 'expected': true},
        {'code': 0, 'expected': false},
        {'code': 40140127, 'expected': false},
        {'code': 500, 'expected': false},
      ];

      for (final testCase in testCases) {
        final responseData = {'code': testCase['code']};
        final needsRefresh = tokenManager.needsTokenRefresh(responseData);
        expect(needsRefresh, equals(testCase['expected']),
            reason: 'Code ${testCase['code']} should ${testCase['expected'] == true ? '' : 'not '}trigger refresh');
      }
    });

    test('应该能够获取Token状态', () async {
      // 保存Token
      await tokenManager.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
        expiry: DateTime.now().add(const Duration(hours: 1)),
        userInfo: {'userId': '123'},
      );

      // 获取状态
      final status = await tokenManager.getTokenStatus();

      expect(status['hasAccessToken'], isTrue);
      expect(status['hasRefreshToken'], isTrue);
      expect(status['hasUserInfo'], isTrue);
      expect(status['isRefreshing'], isFalse);
      expect(status['expiry'], isNotNull);
    });

    test('空Token应该返回false', () async {
      // 不保存任何Token
      final hasValidTokens = await tokenManager.hasValidTokens();
      expect(hasValidTokens, isFalse);

      final accessToken = await tokenManager.getAccessToken();
      final refreshToken = await tokenManager.getRefreshToken();
      expect(accessToken, isNull);
      expect(refreshToken, isNull);
    });

    test('部分Token应该返回false', () async {
      // 只保存access token，不保存refresh token
      await tokenManager.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: '',
      );

      final hasValidTokens = await tokenManager.hasValidTokens();
      expect(hasValidTokens, isFalse);
    });

    test('过期时间为null应该认为即将过期', () async {
      // 保存Token但不设置过期时间
      await tokenManager.saveTokens(
        accessToken: 'test_access_token',
        refreshToken: 'test_refresh_token',
      );

      final isExpiring = await tokenManager.isTokenExpiringSoon();
      expect(isExpiring, isTrue);
    });
  });

  group('TokenManager Integration Tests', () {
    late TokenManager tokenManager;

    setUpAll(() {
      // 确保method channel已经被mock
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
        (MethodCall methodCall) async {
          // Mock implementation - return null for all calls
          return null;
        },
      );
    });

    setUp(() async {
      await GetIt.instance.reset();
      
      // 设置真实的服务（但仍使用mock存储）
      final talkerService = TalkerService();
      talkerService.initialize();
      GetIt.instance.registerSingleton<TalkerService>(talkerService);
      GetIt.instance.registerSingleton<TokenManager>(TokenManager());
      
      tokenManager = GetIt.instance<TokenManager>();
    });

    tearDown(() async {
      await GetIt.instance.reset();
    });

    test('Token保存应该是原子操作', () async {
      // 并发保存Token
      final futures = List.generate(10, (index) {
        return tokenManager.saveTokens(
          accessToken: 'access_$index',
          refreshToken: 'refresh_$index',
          userInfo: {'userId': '$index'},
        );
      });

      await Future.wait(futures);

      // 验证最后保存的Token
      final accessToken = await tokenManager.getAccessToken();
      final refreshToken = await tokenManager.getRefreshToken();
      final userInfo = await tokenManager.getUserInfo();

      expect(accessToken, isNotNull);
      expect(refreshToken, isNotNull);
      expect(userInfo, isNotNull);
      
      // 验证Token是一致的（来自同一次保存）
      final accessIndex = accessToken!.split('_')[1];
      final refreshIndex = refreshToken!.split('_')[1];
      final userIndex = userInfo!['userId'];
      
      expect(accessIndex, equals(refreshIndex));
      expect(accessIndex, equals(userIndex));
    });
  });
} 