package utils

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"go_proxy/internal/net"
)

// FileObj 实现model.Obj接口的结构体
type FileObj struct {
	Name        string
	ModTime_    time.Time
	CreateTime_ time.Time
	FileSize    int64
	Path        string
}

func (o *FileObj) GetName() string {
	return o.Name
}

func (o *FileObj) ModTime() time.Time {
	return o.ModTime_
}

func (o *FileObj) CreateTime() time.Time {
	return o.CreateTime_
}

func (o *FileObj) GetSize() int64 {
	return o.FileSize
}

func (o *FileObj) IsDir() bool {
	return false
}

func (o *FileObj) GetID() string {
	return o.Path
}

func (o *FileObj) GetPath() string {
	return o.Path
}

// GetFileInfoFromURL 从URL获取文件信息
func GetFileInfoFromURL(ctx context.Context, url string, headers http.Header) (string, int64, error) {
	headersClone := headers.Clone()
	headersClone.Del("Range")

	var resp *http.Response
	var err error
	var contentLength int64 = -1

	// 夸克网盘特殊处理
	if strings.Contains(url, "quark.cn") || strings.Contains(url, "aliyundrive.com") {
		// 为夸克网盘设置Range头
		rangeHeaders := headers.Clone()
		rangeHeaders.Set("Range", "bytes=0-0")

		// 发送GET请求并获取Content-Range头
		resp, err = net.RequestHttp(ctx, http.MethodGet, rangeHeaders, url)
		if err != nil {
			return "", -1, fmt.Errorf("夸克网盘或阿里云盘请求失败: %v", err)
		}
		defer resp.Body.Close()

		// 从Content-Range头获取文件大小
		contentRange := resp.Header.Get("Content-Range")
		if contentRange != "" {
			var total int64
			if _, err := fmt.Sscanf(contentRange, "bytes 0-0/%d", &total); err == nil {
				contentLength = total
			}
		}
	} else {
		// 原有逻辑处理非夸克网盘链接
		// 发送HEAD请求获取文件信息
		resp, err = net.RequestHttp(ctx, http.MethodHead, headersClone, url)
		if err != nil {
			// 如果HEAD请求失败，尝试使用GET请求并立即关闭连接
			resp, err = net.RequestHttp(ctx, http.MethodGet, headersClone, url)
			if err != nil {
				return "", -1, fmt.Errorf("无法获取文件信息: %v", err)
			}
			defer resp.Body.Close()
		} else {
			defer resp.Body.Close()
		}

		// 从HTTP响应头获取文件大小
		contentLength = resp.ContentLength
		if contentLength <= 0 {
			contentLength = -1
		}
	}

	// 根据响应获取文件名
	fileName := GetFileNameFromResponse(url, resp)

	return fileName, contentLength, nil
}

// GetFileNameFromResponse 从响应中获取文件名
func GetFileNameFromResponse(url string, resp *http.Response) string {
	// 从URL中提取文件名
	fileName := path.Base(url)

	// 尝试对URL编码的文件名进行解码
	if decodedName, err := URLDecodeFileName(fileName); err == nil && decodedName != "" {
		fileName = decodedName
	}

	// 如果在Content-Disposition头中有文件名，优先使用它
	if contentDisposition := resp.Header.Get("Content-Disposition"); contentDisposition != "" {
		if parsedFileName := ParseContentDisposition(contentDisposition); parsedFileName != "" {
			fileName = parsedFileName
		}
	}

	// 如果无法获取文件名，使用默认名称
	if fileName == "" || fileName == "." || fileName == "/" {
		fileName = "downloaded_file"
		// 对于夸克网盘使用特殊前缀
		if strings.Contains(url, "quark.cn") {
			fileName = "quark_downloaded_file"
		}

		// 尝试从Content-Type添加合适的扩展名
		contentType := resp.Header.Get("Content-Type")
		if contentType != "" {
			switch contentType {
			case "video/mp4":
				fileName += ".mp4"
			case "video/x-matroska":
				fileName += ".mkv"
			case "video/webm":
				fileName += ".webm"
			case "audio/mpeg":
				fileName += ".mp3"
			case "application/pdf":
				fileName += ".pdf"
			}
		}
	}

	return fileName
}

// URLDecodeFileName 尝试从URL编码中解码文件名
func URLDecodeFileName(fileName string) (string, error) {
	decoded, err := url.QueryUnescape(fileName)
	if err != nil {
		return fileName, err
	}

	// 去除可能存在的URL参数
	if index := strings.Index(decoded, "?"); index != -1 {
		decoded = decoded[:index]
	}

	return decoded, nil
}

// ParseContentDisposition 从Content-Disposition头解析文件名
func ParseContentDisposition(contentDisposition string) string {
	if contentDisposition == "" {
		return ""
	}

	// 查找filename部分
	filenameIndex := -1

	// 先查找标准filename
	if index := strings.Index(contentDisposition, "filename="); index != -1 {
		filenameIndex = index + len("filename=")
	}

	// 再查找UTF-8编码的filename
	if index := strings.Index(contentDisposition, "filename*=UTF-8''"); index != -1 {
		filenameIndex = index + len("filename*=UTF-8''")
	}

	if filenameIndex == -1 {
		return ""
	}

	// 获取剩余部分
	remaining := contentDisposition[filenameIndex:]

	// 提取文件名（处理引号）
	var filename string
	if len(remaining) > 0 && (remaining[0] == '"' || remaining[0] == '\'') {
		// 带引号的文件名
		endQuoteIndex := -1
		for i := 1; i < len(remaining); i++ {
			if remaining[i] == remaining[0] {
				endQuoteIndex = i
				break
			}
		}

		if endQuoteIndex != -1 {
			filename = remaining[1:endQuoteIndex]
		}
	} else {
		// 不带引号的文件名（到分号或结束）
		endIndex := len(remaining)
		for i := 0; i < len(remaining); i++ {
			if remaining[i] == ';' {
				endIndex = i
				break
			}
		}

		filename = remaining[:endIndex]
	}

	return filename
}
