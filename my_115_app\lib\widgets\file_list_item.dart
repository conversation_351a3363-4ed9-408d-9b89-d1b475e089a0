import 'package:flutter/material.dart';
import '../models/file_item.dart';
import '../utils/file_type_utils.dart';
import '../utils/image_cache_config.dart';

/// 文件列表项Widget - 列表模式
class FileListTile extends StatelessWidget {
  final FileItem item;
  final VoidCallback onTap;

  const FileListTile({
    super.key,
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: FileTypeUtils.buildFileIcon(item, context: context),
      title: Text(
        item.name,
        style: const TextStyle(fontSize: 16),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: item.isFile && item.formattedSize.isNotEmpty
          ? Text(
              item.formattedSize,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            )
          : null,
      trailing: item.isFolder 
          ? Icon(Icons.chevron_right, color: Colors.grey[400])
          : null,
      onTap: onTap,
    );
  }
}

/// 文件网格项Widget - 网格模式
class FileGridItem extends StatelessWidget {
  final FileItem item;
  final VoidCallback onTap;

  const FileGridItem({
    super.key,
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                child: _buildGridItemImage(context),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(4),
              child: Text(
                item.name,
                style: const TextStyle(fontSize: 12),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建网格项的图片/图标
  Widget _buildGridItemImage(BuildContext context) {
    if (item.isFolder) {
      return Container(
        color: Theme.of(context).brightness == Brightness.dark 
          ? Colors.blue[900] 
          : Colors.blue[50],
        child: Icon(
          Icons.folder,
          size: 50,
          color: Theme.of(context).brightness == Brightness.dark 
            ? Colors.blue[400]! 
            : Colors.blue[600]!,
        ),
      );
    } else if (FileTypeUtils.isImageFile(item) && item.thumb != null && item.thumb!.isNotEmpty) {
      // 显示图片缩略图
      return ImageCacheConfig.buildGridImage(
        imageUrl: item.thumb!,
      );
    } else {
      // 显示文件图标
      return Container(
        color: Theme.of(context).brightness == Brightness.dark 
          ? Colors.grey[850] 
          : Colors.grey[50],
        child: FileTypeUtils.buildFileIcon(item, context: context),
      );
    }
  }
} 