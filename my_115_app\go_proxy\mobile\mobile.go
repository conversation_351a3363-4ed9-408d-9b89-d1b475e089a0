package mobile

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"sync"
	"time"

	"go_proxy/internal/handler"
)

var (
	server     *http.Server
	serverMux  sync.Mutex
	isRunning  bool
	serverPort int = 36150
)

// StartProxyServer 启动代理服务器
// port: 服务器端口，如果为0则使用默认端口36150
// 返回: 实际使用的端口号, 错误信息
func StartProxyServer(port int) (int, error) {
	serverMux.Lock()
	defer serverMux.Unlock()

	if isRunning {
		return serverPort, fmt.Errorf("代理服务器已经在运行中")
	}

	if port <= 0 {
		port = 36150
	}
	serverPort = port

	// 创建HTTP处理函数
	mux := http.NewServeMux()
	mux.HandleFunc("/proxy", handler.ProxyHandler)
	mux.HandleFunc("/health", handler.HealthHandler)

	// 创建服务器
	server = &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	// 在goroutine中启动服务器
	go func() {
		log.Printf("Starting proxy server on :%d...", port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("Server error: %v", err)
			serverMux.Lock()
			isRunning = false
			serverMux.Unlock()
		}
	}()

	// 等待一小段时间确保服务器启动
	time.Sleep(100 * time.Millisecond)
	isRunning = true

	return port, nil
}

// StopProxyServer 停止代理服务器
func StopProxyServer() error {
	serverMux.Lock()
	defer serverMux.Unlock()

	if !isRunning || server == nil {
		return fmt.Errorf("代理服务器未运行")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := server.Shutdown(ctx)
	if err != nil {
		return fmt.Errorf("停止服务器失败: %v", err)
	}

	isRunning = false
	server = nil
	log.Println("Proxy server stopped")

	return nil
}

// IsServerRunning 检查服务器是否正在运行
func IsServerRunning() bool {
	serverMux.Lock()
	defer serverMux.Unlock()
	return isRunning
}

// GetServerPort 获取服务器端口
func GetServerPort() int {
	serverMux.Lock()
	defer serverMux.Unlock()
	if isRunning {
		return serverPort
	}
	return 0
}

// GetProxyURL 获取代理URL
// originalURL: 原始URL
// 返回: 代理URL
func GetProxyURL(originalURL string) string {
	if !isRunning {
		return ""
	}
	return fmt.Sprintf("http://localhost:%d/proxy?url=%s", serverPort, url.QueryEscape(originalURL))
}
