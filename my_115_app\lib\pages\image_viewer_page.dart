import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import '../models/file_item.dart';
import '../utils/image_cache_config.dart';

class ImageViewerPage extends StatefulWidget {
  final List<FileItem> images;
  final int initialIndex;

  const ImageViewerPage({
    super.key,
    required this.images,
    required this.initialIndex,
  });

  @override
  State<ImageViewerPage> createState() => _ImageViewerPageState();
}

class _ImageViewerPageState extends State<ImageViewerPage> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isControlsVisible = true;
  final Set<int> _preloadedIndexes = {}; // 记录已预加载的图片索引

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    
    // 设置全屏模式
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    
    // 延迟执行初始预加载，确保widget已经build完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadImages(widget.initialIndex);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    // 恢复系统UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _toggleControls() {
    setState(() {
      _isControlsVisible = !_isControlsVisible;
    });
  }

  void _previousImage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextImage() {
    if (_currentIndex < widget.images.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  String _getImageUrl(FileItem item) {
    // 优先使用原图URL，如果没有则使用缩略图
    return item.downloadUrl?.isNotEmpty == true 
        ? item.downloadUrl! 
        : (item.thumb?.isNotEmpty == true ? item.thumb! : '');
  }

  /// 预加载周围图片
  void _preloadImages(int currentIndex) {
    // 预加载前面1张图片
    for (int i = 1; i <= 1; i++) {
      final prevIndex = currentIndex - i;
      if (prevIndex >= 0) {
        _preloadImageAtIndex(prevIndex);
      }
    }
    
    // 预加载后面3张图片
    for (int i = 1; i <= 3; i++) {
      final nextIndex = currentIndex + i;
      if (nextIndex < widget.images.length) {
        _preloadImageAtIndex(nextIndex);
      }
    }
  }

  /// 预加载指定索引的图片
  void _preloadImageAtIndex(int index) {
    if (_preloadedIndexes.contains(index)) {
      return; // 已经预加载过，跳过
    }

    final item = widget.images[index];
    final imageUrl = _getImageUrl(item);
    
    if (imageUrl.isNotEmpty) {
      // 标记为已预加载，避免重复加载
      _preloadedIndexes.add(index);
      
      // 使用precacheImage进行预加载
      try {
        precacheImage(
          CachedNetworkImageProvider(imageUrl),
          context,
        ).then((_) {
          debugPrint('预加载成功: 图片 ${index + 1}');
        }).catchError((error) {
          // 预加载失败时从集合中移除，允许下次重试
          _preloadedIndexes.remove(index);
          debugPrint('预加载图片失败: $imageUrl, 错误: $error');
        });
      } catch (e) {
        // 预加载异常时从集合中移除
        _preloadedIndexes.remove(index);
        debugPrint('预加载图片异常: $imageUrl, 错误: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 图片查看器主体
          PhotoViewGallery.builder(
            pageController: _pageController,
            itemCount: widget.images.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
              // 预加载后续图片
              _preloadImages(index);
            },
            builder: (context, index) {
              final item = widget.images[index];
              final imageUrl = _getImageUrl(item);
              
              return PhotoViewGalleryPageOptions.customChild(
                child: imageUrl.isNotEmpty
                    ? ImageCacheConfig.buildViewerImage(
                        imageUrl: imageUrl,
                        placeholder: Container(
                          color: Colors.black,
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          ),
                        ),
                        errorWidget: Container(
                          color: Colors.black,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 80,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                '图片加载失败',
                                style: TextStyle(
                                  color: Colors.grey[400],
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                item.name,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      )
                    : Container(
                        color: Colors.black,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              '无可用图片URL',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              item.name,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained * 0.5,
                maxScale: PhotoViewComputedScale.covered * 2.0,
                heroAttributes: PhotoViewHeroAttributes(
                  tag: 'image_${item.id}',
                ),
                onTapUp: (context, details, controllerValue) {
                  _toggleControls();
                },
              );
            },
            scrollPhysics: const BouncingScrollPhysics(),
            backgroundDecoration: const BoxDecoration(
              color: Colors.black,
            ),
            loadingBuilder: (context, event) => Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          
          // 顶部控制栏
          AnimatedOpacity(
            opacity: _isControlsVisible ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: SafeArea(
                child: Container(
                  height: 56,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                      Expanded(
                        child: Text(
                          widget.images[_currentIndex].name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        '${_currentIndex + 1} / ${widget.images.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // 底部控制栏
          AnimatedOpacity(
            opacity: _isControlsVisible ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Container(
                    height: 80,
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.chevron_left,
                            color: _currentIndex > 0 ? Colors.white : Colors.grey,
                            size: 32,
                          ),
                          onPressed: _currentIndex > 0 ? _previousImage : null,
                        ),
                        Expanded(
                          child: Container(
                            height: 4,
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(2),
                            ),
                            child: LinearProgressIndicator(
                              value: widget.images.isNotEmpty 
                                  ? (_currentIndex + 1) / widget.images.length 
                                  : 0,
                              backgroundColor: Colors.transparent,
                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.chevron_right,
                            color: _currentIndex < widget.images.length - 1 
                                ? Colors.white 
                                : Colors.grey,
                            size: 32,
                          ),
                          onPressed: _currentIndex < widget.images.length - 1 
                              ? _nextImage 
                              : null,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 