# Go Proxy Server

这是一个重构后的Go代理服务器，支持传统HTTP服务器和gomobile集成。

## 项目结构

```
go/
├── cmd/                    # 主程序入口
│   └── main.go            # 传统HTTP服务器
├── mobile/                # gomobile包
│   ├── mobile.go          # 移动端API
│   └── example_test.go    # 使用示例和测试
├── internal/
│   ├── handler/           # HTTP处理器
│   │   └── proxy.go       # 代理请求处理逻辑
│   ├── utils/             # 工具函数
│   │   └── file.go        # 文件信息处理
│   ├── model/             # 数据模型
│   ├── net/               # 网络请求
│   └── stream/            # 流处理
├── server/
│   └── common/            # 公共服务器逻辑
└── pkg/                   # 公共包
    ├── http_range/        # HTTP范围请求
    └── utils/             # 通用工具
```

## 使用方式

### 1. 传统HTTP服务器

直接运行main.go启动HTTP服务器：

```bash
go run cmd/main.go
```

服务器将在端口36150上启动，提供以下端点：
- `/proxy?url=<target_url>` - 代理请求
- `/health` - 健康检查

### 2. gomobile集成

在Flutter或其他移动应用中使用：

```go
import "go_proxy/mobile"

// 启动代理服务器
port, err := mobile.StartProxyServer(36150)
if err != nil {
    // 处理错误
}

// 获取代理URL
originalURL := "https://example.com/video.mp4"
proxyURL := mobile.GetProxyURL(originalURL)

// 在Flutter中使用proxyURL播放视频
// VideoPlayer.network(proxyURL)

// 停止服务器
err = mobile.StopProxyServer()
```

### 3. API说明

#### mobile包提供的函数：

- `StartProxyServer(port int) (int, error)` - 启动代理服务器
- `StopProxyServer() error` - 停止代理服务器
- `IsServerRunning() bool` - 检查服务器是否运行
- `GetServerPort() int` - 获取服务器端口
- `GetProxyURL(originalURL string) string` - 获取代理URL

## 代理功能

### 支持的功能：
- 多线程下载加速
- 自定义请求头
- 范围请求支持
- 夸克网盘和阿里云盘特殊处理
- 文件名自动解析

### 请求参数：
- `url` - 目标URL（必需）
- `thread` - 并发数（可选，默认4）
- `chunkSize` - 分块大小KB（可选，默认1024）

### 自定义请求头：
使用`X-Proxy-Header-`前缀添加自定义请求头：
```
X-Proxy-Header-User-Agent: Mozilla/5.0
X-Proxy-Header-Referer: https://example.com
```

## 编译

### 编译传统程序：
```bash
go build -o proxy cmd/main.go
```

### 编译gomobile库：
```bash
# 安装gomobile
go install golang.org/x/mobile/cmd/gomobile@latest
gomobile init

# 编译Android库
gomobile bind -target=android go_proxy/mobile

# 编译iOS库
gomobile bind -target=ios go_proxy/mobile
```

## 测试

运行测试：
```bash
go test ./mobile -v
```

## Flutter集成示例

在Flutter中使用：

```dart
import 'package:flutter/services.dart';

class ProxyService {
  static const platform = MethodChannel('proxy_service');
  
  static Future<int> startServer(int port) async {
    return await platform.invokeMethod('startServer', {'port': port});
  }
  
  static Future<void> stopServer() async {
    await platform.invokeMethod('stopServer');
  }
  
  static Future<String> getProxyUrl(String originalUrl) async {
    return await platform.invokeMethod('getProxyUrl', {'url': originalUrl});
  }
}
```

## 注意事项

1. 确保端口未被占用
2. 移动端使用时注意网络权限
3. 服务器会在应用退出时自动停止
4. 支持IPv4，监听localhost
5. 默认端口36150，可自定义

## 依赖

- Go 1.24+
- 无外部依赖，使用标准库
