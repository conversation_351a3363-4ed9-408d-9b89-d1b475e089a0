# Flutter集成示例

## 1. 编译gomobile库

```bash
# 进入go目录
cd go

# 编译Android库
gomobile bind -target=android -o proxy.aar go_proxy/mobile

# 编译iOS库  
gomobile bind -target=ios -o Proxy.framework go_proxy/mobile
```

## 2. Flutter项目集成

### Android集成

1. 将`proxy.aar`复制到`android/app/libs/`目录
2. 在`android/app/build.gradle`中添加：

```gradle
dependencies {
    implementation files('libs/proxy.aar')
}
```

### iOS集成

1. 将`Proxy.framework`添加到iOS项目
2. 在Xcode中配置framework

## 3. Dart代码示例

```dart
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';

// 加载动态库
final DynamicLibrary proxyLib = Platform.isAndroid
    ? DynamicLibrary.open('libproxy.so')
    : DynamicLibrary.process();

// 定义函数签名
typedef StartProxyServerC = Int32 Function(Int32 port);
typedef StartProxyServerDart = int Function(int port);

typedef StopProxyServerC = Int32 Function();
typedef StopProxyServerDart = int Function();

typedef GetProxyURLC = Pointer<Utf8> Function(Pointer<Utf8> url);
typedef GetProxyURLDart = Pointer<Utf8> Function(Pointer<Utf8> url);

class ProxyService {
  static final StartProxyServerDart _startServer = proxyLib
      .lookup<NativeFunction<StartProxyServerC>>('StartProxyServer')
      .asFunction();

  static final StopProxyServerDart _stopServer = proxyLib
      .lookup<NativeFunction<StopProxyServerC>>('StopProxyServer')
      .asFunction();

  static final GetProxyURLDart _getProxyURL = proxyLib
      .lookup<NativeFunction<GetProxyURLC>>('GetProxyURL')
      .asFunction();

  static int startServer(int port) {
    return _startServer(port);
  }

  static int stopServer() {
    return _stopServer();
  }

  static String getProxyURL(String originalURL) {
    final urlPtr = originalURL.toNativeUtf8();
    final resultPtr = _getProxyURL(urlPtr);
    final result = resultPtr.toDartString();
    
    malloc.free(urlPtr);
    malloc.free(resultPtr);
    
    return result;
  }
}
```

## 4. 使用示例

```dart
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerScreen extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerScreen({Key? key, required this.videoUrl}) : super(key: key);

  @override
  _VideoPlayerScreenState createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      // 启动代理服务器
      final port = ProxyService.startServer(36150);
      print('代理服务器已启动，端口: $port');

      // 获取代理URL
      final proxyUrl = ProxyService.getProxyURL(widget.videoUrl);
      print('代理URL: $proxyUrl');

      // 初始化视频播放器
      _controller = VideoPlayerController.network(proxyUrl);
      await _controller!.initialize();

      setState(() {
        _isLoading = false;
      });

      // 开始播放
      _controller!.play();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    // 停止代理服务器
    ProxyService.stopServer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Text('错误: $_error'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('视频播放'),
      ),
      body: Center(
        child: _controller!.value.isInitialized
            ? AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: VideoPlayer(_controller!),
              )
            : const CircularProgressIndicator(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            _controller!.value.isPlaying
                ? _controller!.pause()
                : _controller!.play();
          });
        },
        child: Icon(
          _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
        ),
      ),
    );
  }
}
```

## 5. 完整应用示例

```dart
import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Proxy Video Player',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: HomeScreen(),
    );
  }
}

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _urlController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('代理视频播放器'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: '视频URL',
                hintText: '输入视频链接',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final url = _urlController.text.trim();
                if (url.isNotEmpty) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => VideoPlayerScreen(videoUrl: url),
                    ),
                  );
                }
              },
              child: const Text('播放视频'),
            ),
          ],
        ),
      ),
    );
  }
}
```

## 6. 注意事项

1. **权限配置**：确保应用有网络权限
2. **错误处理**：添加适当的错误处理逻辑
3. **生命周期**：在适当的时机启动和停止代理服务器
4. **端口冲突**：检查端口是否被占用
5. **内存管理**：及时释放资源

## 7. 调试技巧

1. 使用`print`语句输出代理URL
2. 检查代理服务器是否正常启动
3. 验证原始URL是否可访问
4. 使用网络抓包工具分析请求
