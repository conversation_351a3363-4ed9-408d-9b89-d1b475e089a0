---

### **开发计划书：基于115开放API的Flutter应用**

#### **项目概述**
本项目旨在使用Flutter框架，创建一个连接115开放平台的第三方客户端。应用核心功能包括用户认证、文件/文件夹列表查看（支持无限滚动与排序）、图片网格预览与全功能查看器，以及全屏视频播放。

#### **基本原则 (与AI沟通时可作为前置条件)**
1.  **状态管理**: 我们将采用 `Provider` 或 `Riverpod` 进行状态管理，以清晰地分离UI和业务逻辑。
2.  **网络请求**: 使用 `http` 包进行API请求。我们会封装一个统一的API服务类来处理请求、添加`Authorization`头以及处理Token刷新。
3.  **本地存储**: 使用 `shared_preferences` 存储用户的`Open Token`和`Open Refresh Token`。
4.  **缓存**:
    *   **图片缓存**: 使用 `cached_network_image` 自动处理图片下载和缓存。
    *   **数据缓存**: 在后期任务中为文件列表实现简单的内存或数据库缓存。
5.  **UI设计**: 遵循Material Design 3 (MD3) 风格，保持界面简洁现代。

---

### **开发路线图 (Roadmap)**

我们将项目分解为以下 **6个核心任务**。请严格按照顺序执行，因为后续任务依赖于前面任务的完成。

#### **任务 1: 项目初始化与API服务核心构建**
**目标**: 搭建App骨架，实现最核心的认证与API请求逻辑，为后续所有功能打下基础。

*   **步骤**:
    1.  **创建Flutter项目**: 初始化一个新的Flutter项目。
    2.  **添加依赖**: 在 `pubspec.yaml` 文件中添加必要的库:
        *   `http`: 用于网络请求。
        *   `shared_preferences`: 用于本地存储Token。
        *   `provider` / `flutter_riverpod`: 用于状态管理。
    3.  **创建认证页面 (Login Page)**:
        *   创建一个简单的UI，包含两个 `TextField` 分别用于输入 "Open Token" 和 "Open Refresh Token"。
        *   一个 "登录" 按钮，点击后将这两个Token保存到 `shared_preferences`，然后跳转到主页。
    4.  **创建API服务类 (`api_service.dart`)**:
        *   封装一个单例或全局可访问的 `ApiService` 类。
        *   实现一个核心的 `get` 和 `post` 方法，这些方法会自动从 `shared_preferences` 读取 "Open Token" 并添加到请求的 `Authorization` header中。
        *   **关键点**: 实现 **Token自动刷新逻辑**。可以创建一个拦截器或在请求方法中加入逻辑：当API返回401或其他认证失败状态码时，自动调用 `https://passportapi.115.com/open/refreshToken` 接口，用保存的 "Refresh Token" 获取新的 "Access Token" 和 "Refresh Token"，更新 `shared_preferences` 中的值，然后自动重新发起刚才失败的请求。
    5.  **启动逻辑**: 在 `main.dart` 中，检查 `shared_preferences` 是否已存在Token。如果存在，直接导航到主页（暂时是个空页面）；如果不存在，则显示认证页面。

---

#### **任务 2: 文件与文件夹列表页面**
**目标**: 创建应用的核心界面，能够展示根目录和子目录的文件与文件夹列表，并支持无限滚动加载。

*   **步骤**:
    1.  **创建文件列表页面 (`file_list_page.dart`)**:
        *   这是一个 `StatefulWidget`，它接收一个可选的目录ID `cid`（根目录 `cid` 通常为 `0`）。
        *   页面需要一个列表来存储文件数据，一个 `ScrollController`，以及加载状态（如 `isLoading`, `hasMore`）。
    2.  **调用API获取数据**:
        *   在页面初始化时，使用 **任务1** 创建的 `ApiService` 调用 `GET /open/ufile/files` 接口（`cid` 为 `0`）。
        *   解析返回的JSON数据，将文件/文件夹列表展示出来。
    3.  **实现列表视图 (`ListView.builder`)**:
        *   根据 `fc` 字段（0为文件夹，1为文件）显示不同的图标（例如 `Icons.folder` 和 `Icons.article`）。
        *   显示 `fn` (文件/文件夹名称)。
    4.  **实现无限滚动**:
        *   监听 `ScrollController` 的滚动位置。当滚动到列表底部时，如果 `hasMore` 为 `true` 且 `isLoading` 为 `false`，则发起下一次API请求（增加 `offset` 参数），并将新数据追加到现有列表中。
    5.  **实现导航**:
        *   当用户点击一个**文件夹**时，使用 `Navigator.push` 跳转到一个**新的 `FileListPage` 实例**，并将被点击文件夹的 `cid` 作为参数传递过去。这自然地形成了页面堆栈，Flutter的返回按钮可以直接使用。

---

#### **任务 3: 增强列表页 - 面包屑导航与排序**
**目标**: 提升文件列表页的可用性，增加路径导航和排序功能。

*   **步骤**:
    1.  **实现面包屑导航栏 (Breadcrumbs)**:
        *   `GET /open/ufile/files` 接口返回的 `path` 字段是一个包含所有父目录信息的数组。
        *   在 `FileListPage` 的顶部创建一个横向滚动的 `Row` 或 `ListView`。
        *   根据 `path` 数组动态生成面包屑导航，例如 `根目录 > 电影 > 2023`。
        *   点击面包屑的某个路径（如 "电影"），应该能使用 `Navigator.popUntil` 返回到对应的页面。
    2.  **实现排序功能**:
        *   在 `AppBar` 上添加一个排序图标按钮。
        *   点击按钮后，弹出一个对话框或菜单 (`PopupMenuButton`)，提供排序选项：按文件名、大小、修改时间。每个选项还应包含升序/降序。
        *   当用户选择一个新的排序方式后，清空当前列表数据，重置 `offset` 为 `0`，然后使用新的 `o` 和 `asc` 参数重新调用API获取数据。

---

#### **任务 4: 图片网格预览与全功能查看器**
**目标**: 为包含图片的文件夹提供更友好的网格预览，并实现一个功能完善的图片查看器。

*   **步骤**:
    1.  **创建图片网格视图**:
        *   在 `FileListPage` 中增加逻辑判断。例如，如果用户通过筛选功能选择了 "图片" (`type=2`)，或者当前文件夹内图片文件占多数，则使用 `GridView.builder` 替代 `ListView.builder`。
        *   在网格的每个单元格中，使用 `cached_network_image` 包加载并显示 `thumb` (缩略图) URL。设置好占位符和错误组件。
    2.  **创建图片查看器页面 (`image_viewer_page.dart`)**:
        *   这个页面接收一个图片列表（包含所有图片的下载信息，特别是原图地址 `uo`）和被点击图片的初始索引。
    3.  **实现左右滑动切换 (`PageView.builder`)**:
        *   使用 `PageView.builder` 作为页面的主体，这样用户可以左右滑动来切换上一张/下一张图片。
    4.  **实现缩放与拖动 (`photo_view` 包)**:
        *   在 `PageView` 的每个页面中，使用 `photo_view` 这个强大的包来包裹 `cached_network_image`。`photo_view` 可以非常简单地实现双指缩放、拖动等功能。

---

#### **任务 5: 视频播放器**
**目标**: 实现点击视频文件后，获取播放地址并进行全屏播放。

*   **步骤**:
    1.  **添加依赖**: 在 `pubspec.yaml` 中添加 `video_player` 和 `chewie`（`chewie` 提供了带控件的UI，能极大简化开发）。
    2.  **获取播放地址**:
        *   在 `FileListPage` 中，当用户点击一个视频文件时，首先使用 `ApiService` 调用 `POST /open/ufile/downurl` 接口，并传递该文件的 `pc` (pick_code)。
        *   解析返回的JSON，获取到真正的视频播放URL。
    3.  **创建视频播放页面 (`video_player_page.dart`)**:
        *   该页面接收视频播放URL作为参数。
    4.  **初始化播放器**:
        *   **关键点**: 根据API文档要求，视频播放请求的 `User-Agent` 需要和获取播放地址接口的UA一致。`video_player` 的 `VideoPlayerController.networkUrl` 构造函数接受 `httpHeaders` 参数，在这里传入 `{'User-Agent': '你自己的UA'}`。
        *   使用 `video_player` 和 `chewie` 初始化播放器。
        *   配置 `chewie` 以自动播放、进入全屏并显示标准控件。

---

#### **任务 6: 性能优化与错误处理**
**目标**: 提升应用的流畅度和健壮性，包括数据缓存和更友好的用户提示。

*   **步骤**:
    1.  **实现数据缓存**:
        *   为 `ApiService` 增加一个内存缓存机制。可以用一个 `Map<String, dynamic>` 来存储 `cid` 到文件列表JSON数据的映射。
        *   在请求文件列表前，先检查缓存中是否存在该 `cid` 的有效数据。如果存在，直接返回缓存数据；如果不存在，则发起网络请求，成功后将结果存入缓存。可以为缓存设置一个简单的过期时间（例如5分钟）。
    2.  **优化UI反馈**:
        *   在 `FileListPage` 中，当 `isLoading` 为 `true` 时，在列表底部显示一个加载动画 (`CircularProgressIndicator`)。
        *   当API请求失败（如超时、网络错误），在页面中央显示错误信息和一个 "重试" 按钮。
    3.  **API频率控制 (Throttling/Debouncing)**:
        *   考虑到 `1s/2次` 的API限制，为无限滚动加载添加一个简单的防抖或节流逻辑，防止用户快速来回滚动时触发过多的API请求。

---