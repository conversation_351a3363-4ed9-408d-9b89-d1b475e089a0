package model

import (
	"sort"
	"time"
)

type ObjUnwrap interface {
	Unwrap() Obj
}

type Obj interface {
	GetSize() int64
	GetName() string
	ModTime() time.Time
	CreateTime() time.Time
	IsDir() bool

	// The internal information of the driver.
	// If you want to use it, please understand what it means
	GetID() string
	GetPath() string
}

type UpdateProgress func(percentage float64)

type URL interface {
	URL() string
}

type Thumb interface {
	Thumb() string
}

type SetPath interface {
	SetPath(path string)
}

func ExtractFolder(objs []Obj, extractFolder string) {
	if extractFolder == "" {
		return
	}
	front := extractFolder == "front"
	sort.SliceStable(objs, func(i, j int) bool {
		if objs[i].IsDir() || objs[j].IsDir() {
			if !objs[i].IsDir() {
				return !front
			}
			if !objs[j].IsDir() {
				return front
			}
		}
		return false
	})
}

func UnwrapObj(obj Obj) Obj {
	if unwrap, ok := obj.(ObjUnwrap); ok {
		obj = unwrap.Unwrap()
	}
	return obj
}

func GetThumb(obj Obj) (thumb string, ok bool) {
	if obj, ok := obj.(Thumb); ok {
		return obj.Thumb(), true
	}
	if unwrap, ok := obj.(ObjUnwrap); ok {
		return GetThumb(unwrap.Unwrap())
	}
	return thumb, false
}

func GetUrl(obj Obj) (url string, ok bool) {
	if obj, ok := obj.(URL); ok {
		return obj.URL(), true
	}
	if unwrap, ok := obj.(ObjUnwrap); ok {
		return GetUrl(unwrap.Unwrap())
	}
	return url, false
}
