# 115 API 错误码处理修改说明

## 修改概述

根据115 API的特性，即使在认证失败等错误情况下也返回HTTP 200状态码，真正的错误信息在响应body的`code`字段中。本次修改更新了ApiService和相关页面的错误处理逻辑。

## 修改的主要内容

### 1. ApiService 类的增强

#### 新增错误码常量
```dart
static const int codeAccessTokenInvalid = 40140125; // access_token 无效
static const int codeRefreshTokenInvalid = 40140116; // refresh_token 无效
static const int codeRefreshTokenExpired = 40140119; // refresh_token 已过期
static const int codeAccessTokenRefreshFailed = 40140121; // access_token 刷新失败
static const int codeAccessTokenTooFrequent = 40140117; // access_token 刷新太频繁
```

#### 新增错误处理方法
- `_parseResponse()`: 安全解析HTTP响应
- `_needsTokenRefresh()`: 检查是否需要刷新token
- `_needsRelogin()`: 检查是否需要重新登录
- `_shouldRetry()`: 检查是否应该重试
- `_isRefreshTooFrequent()`: 检查刷新是否过于频繁
- `_handleTokenErrors()`: 统一处理token相关错误

#### 修改的方法
- `get()`: 支持自动token刷新和错误处理
- `post()`: 支持自动token刷新和错误处理  
- `postFormData()`: 支持自动token刷新和错误处理
- `getPlayUrl()`: 正确检查115错误码
- `getBatchPlayUrls()`: 正确检查115错误码
- `isTokenValid()`: 正确验证token状态
- `_refreshAccessToken()`: 正确处理刷新token时的错误

### 2. 页面级修改

#### FileListPage
- `_fetchFiles()`: 添加115错误码检查，提供更准确的错误信息

## 错误码处理流程

### Token相关错误的自动处理
1. **40140125 (access_token无效)**: 自动刷新token并重试请求
2. **40140116/40140119 (refresh_token无效/过期)**: 清除本地token，需要重新登录
3. **40140117 (刷新太频繁)**: 暂停处理，避免过度请求
4. **40140121 (刷新失败)**: 可以重试

### 使用示例

#### 在业务代码中处理错误码
```dart
final response = await apiService.get('/open/ufile/files');
final data = jsonDecode(response.body);

// 检查是否有错误码
if (data['code'] != null && data['code'] != 0) {
  // 处理具体的错误码
  switch (data['code']) {
    case 40140125:
      // access_token无效 - ApiService会自动处理
      break;
    case 40140116:
    case 40140119:
      // refresh_token问题 - 需要重新登录
      Navigator.pushReplacementNamed(context, '/login');
      break;
    default:
      throw Exception('API错误: ${data['code']} - ${data['message']}');
  }
}
```

## 主要改进

1. **自动化token管理**: ApiService现在能够自动检测token过期并尝试刷新
2. **准确的错误处理**: 基于115真实的错误码而不是HTTP状态码
3. **用户体验优化**: 减少用户手动重新登录的需要
4. **错误信息改进**: 提供更具体的错误信息帮助调试

## 兼容性说明

- 所有现有的API调用方式保持不变
- 错误处理现在更加智能和自动化
- 如果遇到需要重新登录的错误码，会自动清除本地token

## 注意事项

1. **HTTP状态码vs错误码**: 115 API的HTTP状态码通常为200，真正的错误信息在响应body中
2. **Token生命周期**: 系统现在能够自动管理token的生命周期
3. **错误重试**: 对于可重试的错误，系统会自动处理
4. **频率限制**: 系统会检测刷新频率过快的情况并适当处理 