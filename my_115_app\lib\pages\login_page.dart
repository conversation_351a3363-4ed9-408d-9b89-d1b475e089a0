import 'package:flutter/material.dart';
import '../services/token_manager.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _openTokenController = TextEditingController();
  final _refreshTokenController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _openTokenController.dispose();
    _refreshTokenController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final tokenManager = TokenManager();
      await tokenManager.saveTokens(
        accessToken: _openTokenController.text.trim(),
        refreshToken: _refreshTokenController.text.trim(),
      );
      
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存Token失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('115 API 客户端'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud,
                size: 80,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 32),
              TextFormField(
                controller: _openTokenController,
                decoration: const InputDecoration(
                  labelText: 'Open Token',
                  helperText: '请输入您的115开放平台Access Token',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入Open Token';
                  }
                  return null;
                },
                maxLines: 3,
                minLines: 1,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _refreshTokenController,
                decoration: const InputDecoration(
                  labelText: 'Open Refresh Token',
                  helperText: '请输入您的115开放平台Refresh Token',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入Open Refresh Token';
                  }
                  return null;
                },
                maxLines: 3,
                minLines: 1,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _login,
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : const Text('登录'),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '请前往115开放平台获取您的Token',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 