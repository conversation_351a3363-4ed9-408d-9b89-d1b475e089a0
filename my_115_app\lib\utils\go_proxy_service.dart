import 'package:flutter/services.dart';

class GoProxyService {
  static const _channel = MethodChannel('com.example/goproxy');

  /// 启动 Go 代理服务器。
  /// [port] 指定端口，如果为 0 或不提供，则使用 Go 端的默认值。
  /// 返回实际监听的端口号。
  static Future<int> startProxy({int port = 0}) async {
    try {
      final int actualPort = await _channel.invokeMethod('startProxy', {'port': port});
      return actualPort;
    } on PlatformException catch (e) {
      print("启动代理失败: ${e.message}");
      if (e.message?.contains("运行中") ?? false) {
        return 0;
      }
      throw Exception("Failed to start Go proxy: ${e.message}");
    }
  }

  /// 停止 Go 代理服务器。
  static Future<void> stopProxy() async {
    try {
      await _channel.invokeMethod('stopProxy');
    } on PlatformException catch (e) {
      print("停止代理失败: ${e.message}");
    }
  }

  /// 检查服务器是否正在运行。
  static Future<bool> isServerRunning() async {
    try {
      return await _channel.invokeMethod('isServerRunning') ?? false;
    } on PlatformException catch (e) {
      print("检查代理状态失败: ${e.message}");
      return false;
    }
  }

  /// 获取经过代理的 URL。
  /// 如果服务器未运行，将返回空字符串。
  static Future<String> getProxyUrl(String originalUrl) async {
    try {
      return await _channel.invokeMethod('getProxyUrl', {'originalUrl': originalUrl}) ?? '';
    } on PlatformException catch (e) {
      print("获取代理 URL 失败: ${e.message}");
      return '';
    }
  }
}