{"buildFiles": ["D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter-workspace\\my_115_app\\build\\.cxx\\Debug\\441p2u48\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter-workspace\\my_115_app\\build\\.cxx\\Debug\\441p2u48\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}