import 'package:flutter/material.dart';
import '../models/file_item.dart';

/// 文件类型枚举
enum FileType {
  folder,
  image,
  video,
  audio,
  document,
  pdf,
  archive,
  unknown,
}

/// 文件类型工具类
class FileTypeUtils {
  // 图片文件扩展名
  static const Set<String> _imageExtensions = {
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tga'
  };

  // 视频文件扩展名
  static const Set<String> _videoExtensions = {
    // 常见格式
    'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'm4v', 'mpg', 'mpeg', 'webm',
    // Media Kit 额外支持的格式
    'ogv', 'ogg', '3gp', '3g2', 'f4v', 'f4p', 'f4a', 'f4b',
    'vob', 'mts', 'm2ts', 'ts', 'mxf', 'rm', 'rmvb', 'asf',
    // 更多高级格式
    'hevc', 'h264', 'h265', 'vp8', 'vp9', 'av1',
    // 容器格式
    'mka', 'mks', 'divx', 'xvid', 'dv', 'mp2', 'm1v', 'm2v'
  };

  // 音频文件扩展名
  static const Set<String> _audioExtensions = {
    'mp3', 'wav', 'flac', 'aac', 'm4a', 'wma', 'ogg', 'opus', 'aiff', 'au'
  };

  // 文档文件扩展名
  static const Set<String> _documentExtensions = {
    'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp'
  };

  // PDF文件扩展名
  static const Set<String> _pdfExtensions = {'pdf'};

  // 压缩文件扩展名
  static const Set<String> _archiveExtensions = {
    'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'arj', 'cab', 'lzh'
  };

  /// 获取文件扩展名
  static String _getFileExtension(String fileName) {
    final lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex == -1 || lastDotIndex == fileName.length - 1) {
      return '';
    }
    return fileName.substring(lastDotIndex + 1).toLowerCase();
  }

  /// 获取文件类型
  static FileType getFileType(FileItem item) {
    if (item.isFolder) return FileType.folder;

    final extension = _getFileExtension(item.name);
    
    if (_imageExtensions.contains(extension)) return FileType.image;
    if (_videoExtensions.contains(extension)) return FileType.video;
    if (_audioExtensions.contains(extension)) return FileType.audio;
    if (_documentExtensions.contains(extension)) return FileType.document;
    if (_pdfExtensions.contains(extension)) return FileType.pdf;
    if (_archiveExtensions.contains(extension)) return FileType.archive;
    
    return FileType.unknown;
  }

  /// 检查是否为图片文件
  static bool isImageFile(FileItem item) {
    return getFileType(item) == FileType.image;
  }

  /// 检查是否为视频文件
  static bool isVideoFile(FileItem item) {
    return getFileType(item) == FileType.video;
  }

  /// 检查是否为音频文件
  static bool isAudioFile(FileItem item) {
    return getFileType(item) == FileType.audio;
  }

  /// 获取文件图标
  static Widget buildFileIcon(FileItem item, {double size = 40, BuildContext? context}) {
    final fileType = getFileType(item);
    
    IconData iconData;
    Color color;
    
    // 获取主题相关的颜色，如果没有context则使用默认颜色
    final isDark = context != null ? Theme.of(context).brightness == Brightness.dark : false;
    
    switch (fileType) {
      case FileType.folder:
        iconData = Icons.folder;
        color = isDark ? Colors.blue[400]! : Colors.blue[600]!;
        break;
      case FileType.image:
        iconData = Icons.image;
        color = isDark ? Colors.green[400]! : Colors.green;
        break;
      case FileType.video:
        iconData = Icons.videocam;
        color = isDark ? Colors.red[400]! : Colors.red;
        break;
      case FileType.audio:
        iconData = Icons.audiotrack;
        color = isDark ? Colors.purple[400]! : Colors.purple;
        break;
      case FileType.pdf:
        iconData = Icons.picture_as_pdf;
        color = isDark ? Colors.red[400]! : Colors.red[700]!;
        break;
      case FileType.document:
        iconData = Icons.description;
        color = isDark ? Colors.blue[400]! : Colors.blue[700]!;
        break;
      case FileType.archive:
        iconData = Icons.archive;
        color = isDark ? Colors.orange[400]! : Colors.orange;
        break;
      case FileType.unknown:
        iconData = Icons.insert_drive_file;
        color = isDark ? Colors.grey[400]! : Colors.grey[600]!;
    }
    
    return Icon(iconData, color: color, size: size);
  }

  /// 获取文件类型的颜色
  static Color getFileTypeColor(FileItem item, {BuildContext? context}) {
    final fileType = getFileType(item);
    
    // 获取主题相关的颜色，如果没有context则使用默认颜色
    final isDark = context != null ? Theme.of(context).brightness == Brightness.dark : false;
    
    switch (fileType) {
      case FileType.folder:
        return isDark ? Colors.blue[400]! : Colors.blue[600]!;
      case FileType.image:
        return isDark ? Colors.green[400]! : Colors.green;
      case FileType.video:
        return isDark ? Colors.red[400]! : Colors.red;
      case FileType.audio:
        return isDark ? Colors.purple[400]! : Colors.purple;
      case FileType.pdf:
        return isDark ? Colors.red[400]! : Colors.red[700]!;
      case FileType.document:
        return isDark ? Colors.blue[400]! : Colors.blue[700]!;
      case FileType.archive:
        return isDark ? Colors.orange[400]! : Colors.orange;
      case FileType.unknown:
        return isDark ? Colors.grey[400]! : Colors.grey[600]!;
    }
  }

  /// 获取文件类型的显示名称
  static String getFileTypeDisplayName(FileItem item) {
    final fileType = getFileType(item);
    
    switch (fileType) {
      case FileType.folder:
        return '文件夹';
      case FileType.image:
        return '图片';
      case FileType.video:
        return '视频';
      case FileType.audio:
        return '音频';
      case FileType.pdf:
        return 'PDF文档';
      case FileType.document:
        return '文档';
      case FileType.archive:
        return '压缩包';
      case FileType.unknown:
        return '文件';
    }
  }
} 