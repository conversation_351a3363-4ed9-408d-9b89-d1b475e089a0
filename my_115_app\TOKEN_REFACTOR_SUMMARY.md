# Token管理重构总结

## 重构目标

简化token刷新逻辑，实现：
- **检查特定错误码自动刷新token**
- **刷新失败时提示用户**
- **其他地方正常调用接口**

## 具体实现

### 错误码检查
当API返回以下错误码时自动刷新token：
- `40140123`
- `40140124` 
- `40140125`
- `40140126`

### 刷新逻辑
1. 检测到上述错误码时，调用`TokenManager.refreshAccessToken()`
2. 刷新成功 → 重试原请求
3. 刷新失败 → 显示SnackBar提示用户"Token刷新失败，请重新登录"

## 核心文件

### `lib/services/token_manager.dart`
- 集中管理token的获取、保存、清除
- 提供`needsTokenRefresh()`方法检查错误码
- 提供`refreshAccessToken()`方法刷新token

### `lib/services/api_service.dart`
- 在`_handleTokenRefresh()`方法中处理token刷新逻辑
- 所有HTTP方法（GET、POST、postFormData）都会调用此方法
- 刷新失败时通过`ScaffoldMessenger`显示错误提示

## 使用方式

### 普通API调用（自动处理token）
```dart
// 自动检查错误码并刷新token，无需额外处理
final response = await apiService.get('/api/files');
final response = await apiService.post('/api/upload', body: data);
```

### 需要显示错误提示的调用
```dart
// 传入context参数，刷新失败时会显示SnackBar
final response = await apiService.get('/api/files', context: context);
```

## 简化改进

相比之前的复杂实现，现在的版本：
- ✅ **更简单**：只检查4个特定错误码
- ✅ **更直接**：直接在HTTP方法中处理，无需额外的拦截器
- ✅ **更明确**：刷新失败直接提示用户，不做复杂的错误分类
- ✅ **更轻量**：删除了不必要的抽象层和复杂逻辑

## 总结

现在的实现完全按照用户需求：检查特定错误码 → 刷新token → 失败时提示用户，简单有效。 