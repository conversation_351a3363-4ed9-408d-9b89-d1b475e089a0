package com.example.my_115_app

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import mobile.Mobile // 导入 gomobile 生成的绑定类

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example/goproxy"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            try {
                when (call.method) {
                    "startProxy" -> {
                        // Go 函数需要 int，但 invokeMethod 传递的是 Any?，需要安全转换
                        val port = call.argument<Int>("port") ?: 0 
                        // 调用 Go 的 StartProxyServer
                        val actualPort = Mobile.startProxyServer(port.toLong()) // Go int is 64-bit, so use Long
                        result.success(actualPort)
                    }
                    "stopProxy" -> {
                        // 调用 Go 的 StopProxyServer
                        Mobile.stopProxyServer()
                        result.success(null)
                    }
                    "isServerRunning" -> {
                        // 调用 Go 的 IsServerRunning
                        val isRunning = Mobile.isServerRunning()
                        result.success(isRunning)
                    }
                    "getProxyUrl" -> {
                        val originalUrl = call.argument<String>("originalUrl")
                        if (originalUrl == null) {
                            result.error("INVALID_ARGS", "originalUrl is required", null)
                            return@setMethodCallHandler
                        }
                        // 调用 Go 的 GetProxyURL
                        val proxyUrl = Mobile.getProxyURL(originalUrl)
                        result.success(proxyUrl)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            } catch (e: Exception) {
                // gomobile 会将 Go 的 error 转换为 Exception
                result.error("GO_ERROR", e.message, e.stackTraceToString())
            }
        }
    }
}
