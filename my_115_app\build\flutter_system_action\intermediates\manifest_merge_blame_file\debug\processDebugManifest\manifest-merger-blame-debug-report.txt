1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.flutter_system_action.flutter_system_action" >
4
5    <uses-sdk android:minSdkVersion="19" />
6
7    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_system_action-1.0.4\android\src\main\AndroidManifest.xml:4:5-79
7-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_system_action-1.0.4\android\src\main\AndroidManifest.xml:4:22-76
8    <uses-permission android:name="android.permission.CAMERA" />
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_system_action-1.0.4\android\src\main\AndroidManifest.xml:5:5-65
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_system_action-1.0.4\android\src\main\AndroidManifest.xml:5:22-62
9
10</manifest>
