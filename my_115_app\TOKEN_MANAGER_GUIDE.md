# 健壮的Token管理器使用指南

## 概述

现代化的Token管理系统采用了企业级架构设计，具备以下核心特性：

- ✅ **安全存储**: 使用 `flutter_secure_storage` 替代 `shared_preferences`
- ✅ **自动拦截**: 使用 `dio` 拦截器自动处理Token添加和刷新
- ✅ **依赖注入**: 使用 `get_it` 进行依赖管理
- ✅ **并发安全**: 防止多个请求同时刷新Token
- ✅ **主动刷新**: Token即将过期时自动刷新
- ✅ **完整日志**: 所有Token操作都有详细日志记录

## 架构组件

### 1. TokenManager (核心)
- 安全的Token存储和读取
- 智能的Token刷新机制
- 并发安全的实现

### 2. AuthInterceptor (拦截器)
- 自动添加Authorization头
- 处理401错误和业务错误码
- 自动重试失败的请求

### 3. ServiceLocator (依赖注入)
- 统一的服务管理
- 便捷的依赖访问

## 快速开始

### 第1步：应用初始化

在 `main.dart` 中初始化服务：

```dart
import 'package:flutter/material.dart';
import 'services/service_locator.dart';
import 'services/api_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化服务定位器
  await ServiceLocator.setupServices();
  
  // 初始化API服务
  ApiService().initialize();
  
  runApp(MyApp());
}
```

### 第2步：保存登录Token

```dart
import 'package:get_it/get_it.dart';
import 'services/token_manager.dart';

// 登录成功后保存Token
Future<void> saveLoginTokens(String accessToken, String refreshToken) async {
  final tokenManager = GetIt.instance<TokenManager>();
  
  await tokenManager.saveTokens(
    accessToken: accessToken,
    refreshToken: refreshToken,
    expiry: DateTime.now().add(Duration(hours: 1)), // 设置过期时间
    userInfo: {
      'userId': '12345',
      'username': '<EMAIL>',
      // 其他用户信息
    },
  );
}
```

### 第3步：使用API服务

```dart
import 'services/api_service.dart';

class FileService {
  final ApiService _apiService = ApiService();
  
  // GET请求示例
  Future<Map<String, dynamic>> getFileList(String folderId) async {
    final response = await _apiService.get(
      '/open/file/list',
      queryParams: {
        'aid': '1',
        'cid': folderId,
        'limit': '50',
      },
      useCache: true,
      cacheTtl: Duration(minutes: 5),
    );
    
    return response.data as Map<String, dynamic>;
  }
  
  // POST请求示例
  Future<void> createFolder(String name, String parentId) async {
    await _apiService.post(
      '/open/file/add',
      data: {
        'pid': parentId,
        'cname': name,
      },
    );
  }
  
  // 表单数据请求示例
  Future<String> getPlayUrl(String pickCode) async {
    return await _apiService.getPlayUrl(pickCode);
  }
}
```

### 第4步：检查Token状态

```dart
import 'services/api_service.dart';

class AuthService {
  final ApiService _apiService = ApiService();
  
  Future<bool> checkTokenValid() async {
    final result = await _apiService.isTokenValid();
    
    switch (result.status) {
      case TokenValidationStatus.valid:
        print('Token有效');
        return true;
        
      case TokenValidationStatus.noToken:
        print('没有Token，需要登录');
        return false;
        
      case TokenValidationStatus.needsRelogin:
        print('需要重新登录: ${result.errorMessage}');
        await _apiService.clearTokens();
        return false;
        
      case TokenValidationStatus.networkError:
        print('网络错误: ${result.errorMessage}');
        return false;
        
      default:
        print('未知错误: ${result.errorMessage}');
        return false;
    }
  }
}
```

## 高级特性

### 1. 请求取消

```dart
import 'package:dio/dio.dart';

final cancelToken = CancelToken();

// 发起可取消的请求
final response = await _apiService.get(
  '/open/file/list',
  cancelToken: cancelToken,
);

// 取消请求
cancelToken.cancel('User cancelled');
```

### 2. 上传文件（带进度）

```dart
await _apiService.uploadFile(
  filePath: '/path/to/file.jpg',
  fileName: 'image.jpg',
  uploadPath: '/uploads',
  onSendProgress: (count, total) {
    final progress = count / total;
    print('上传进度: ${(progress * 100).toInt()}%');
  },
);
```

### 3. 批量请求

```dart
// 批量获取播放地址
final playUrls = await _apiService.getBatchPlayUrls([
  'pick_code_1',
  'pick_code_2',
  'pick_code_3',
]);

for (final entry in playUrls.entries) {
  print('${entry.key}: ${entry.value}');
}
```

### 4. 调试和监控

```dart
// 获取Token状态
final tokenStatus = await _apiService.getTokenStatus();
print('Token状态: $tokenStatus');

// 获取API日志
final logs = _apiService.getRecentLogs(10);
for (final log in logs) {
  print('${log.timestamp}: ${log.method} ${log.endpoint} -> ${log.statusCode}');
}

// 获取缓存统计
final cacheStats = _apiService.getCacheStats();
print('缓存统计: $cacheStats');
```

## 依赖配置

在 `pubspec.yaml` 中添加所需依赖：

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # 网络请求
  dio: ^5.3.3
  
  # 安全存储
  flutter_secure_storage: ^9.0.0
  
  # 依赖注入
  get_it: ^7.6.4
```

## 配置选项

### 1. 修改超时设置

在 `service_locator.dart` 中：

```dart
static Dio _createDioInstance() {
  final dio = Dio(BaseOptions(
    baseUrl: 'https://proapi.115.com',
    connectTimeout: const Duration(seconds: 30), // 修改连接超时
    receiveTimeout: const Duration(seconds: 60), // 修改接收超时
    sendTimeout: const Duration(seconds: 30),    // 修改发送超时
  ));
  // ...
}
```

### 2. 自定义拦截器

```dart
class CustomInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 自定义请求逻辑
    handler.next(options);
  }
}

// 在ServiceLocator中添加
dio.interceptors.add(CustomInterceptor());
```

### 3. 配置日志级别

```dart
final apiService = ApiService();

// 只记录错误日志
apiService.setLogEnabledLevels({'ERROR'});

// 禁用控制台输出
apiService.setLogConsoleOutput(false);
```

## 安全最佳实践

1. **Token存储**: 所有敏感信息都使用flutter_secure_storage加密存储
2. **网络安全**: 支持证书固定（可选配置）
3. **请求签名**: 可扩展支持请求签名验证
4. **日志安全**: 生产环境可禁用敏感信息日志

## 错误处理

### 1. 网络错误

```dart
try {
  final response = await _apiService.get('/api/data');
  // 处理成功响应
} on DioException catch (e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
      print('连接超时');
      break;
    case DioExceptionType.receiveTimeout:
      print('接收超时');
      break;
    case DioExceptionType.connectionError:
      print('连接错误');
      break;
    default:
      print('其他错误: ${e.message}');
  }
}
```

### 2. 业务错误

```dart
final response = await _apiService.get('/api/data');
final data = response.data as Map<String, dynamic>;

if (data['state'] != true) {
  throw Exception('业务错误: ${data['message']}');
}
```

## 性能优化

1. **智能缓存**: 自动缓存GET请求结果，支持TTL配置
2. **请求合并**: 防止相同请求重复发送
3. **连接复用**: Dio自动管理HTTP连接池
4. **gzip压缩**: 自动启用响应压缩

## 故障排除

### 常见问题

1. **服务未初始化**: 确保在使用前调用了`ServiceLocator.setupServices()`
2. **Token刷新失败**: 检查refresh_token是否有效
3. **网络请求失败**: 检查网络连接和代理设置

### 调试工具

```dart
// 启用详细日志
apiService.setLogEnabledLevels({'DEBUG', 'INFO', 'WARNING', 'ERROR'});

// 查看所有日志
final allLogs = apiService.getAllLogs();
print('共有 ${allLogs.length} 条日志');

// 搜索特定日志
final errorLogs = apiService.searchLogs('error');
```

## 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │   ApiService    │    │  TokenManager   │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │    UI     │──┼────┼─▶│    Dio    │  │    │  │  Secure   │  │
│  └───────────┘  │    │  └───────────┘  │    │  │  Storage  │  │
└─────────────────┘    │       │         │    │  └───────────┘  │
                       │       ▼         │    └─────────────────┘
┌─────────────────┐    │  ┌───────────┐  │    ┌─────────────────┐
│ ServiceLocator  │    │  │Interceptor│  │    │   LogService    │
│  (Dependency    │    │  │ Pipeline  │  │    │                 │
│   Injection)    │    │  └───────────┘  │    │  ┌───────────┐  │
│                 │    │       │         │    │  │    Logs   │  │
│  ┌───────────┐  │    │       ▼         │    │  └───────────┘  │
│  │   GetIt   │  │    │  ┌───────────┐  │    └─────────────────┘
│  └───────────┘  │    │  │   Auth    │  │
└─────────────────┘    │  │Interceptor│  │
                       │  └───────────┘  │
                       └─────────────────┘
```

## 总结

现代化的Token管理系统提供了：

- 🔒 **更高的安全性**: 敏感数据加密存储
- 🚀 **更好的性能**: 智能缓存和连接复用
- 🛠 **更强的可维护性**: 依赖注入和模块化设计
- 📊 **更完善的监控**: 详细的日志和统计信息
- 🔄 **更智能的刷新**: 自动处理Token生命周期
- ⚡ **更简单的API**: 统一的调用接口
- 🔧 **更灵活的配置**: 可定制的拦截器和选项

这个系统适用于所有规模的Flutter项目，从简单的个人应用到复杂的企业级应用。 