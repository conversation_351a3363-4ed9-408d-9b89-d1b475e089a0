package handler

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"go_proxy/internal/model"
	"go_proxy/internal/utils"
	"go_proxy/server/common"
)

// ProxyHandler 代理请求处理器
func ProxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取要代理的URL从查询参数
	targetURL := r.URL.Query().Get("url")
	if targetURL == "" {
		http.Error(w, "missing url parameter", http.StatusBadRequest)
		return
	}

	// 从请求中复制需要传递的请求头
	// 定义不应该转发的请求头
	ignoreHeaders := []string{
		"host",
		"connection",
		"content-length",
		"upgrade-insecure-requests",
		"accept-encoding", // 有时需要保留原始响应格式
		"sec-fetch-mode",
		"sec-fetch-site",
		"sec-fetch-user",
		"sec-fetch-dest",
	}

	customHeaders := http.Header{}
	for key, values := range r.Header {
		// 转换为小写来检查
		keyLower := strings.ToLower(key)
		// 检查是否在忽略列表中
		ignore := false
		for _, h := range ignoreHeaders {
			if keyLower == h {
				ignore = true
				break
			}
		}
		if !ignore {
			for _, value := range values {
				customHeaders.Add(key, value)
			}
		}
	}

	// 如果有X-Proxy-Header-前缀的头部，以其值覆盖相应的自定义头
	for key, values := range r.Header {
		if strings.HasPrefix(key, "X-Proxy-Header-") {
			// 去掉X-Proxy-Header-前缀
			newKey := strings.TrimPrefix(key, "X-Proxy-Header-")
			// 先清除可能存在的同名头
			customHeaders.Del(newKey)
			// 添加自定义值
			for _, value := range values {
				customHeaders.Add(newKey, value)
			}
		}
	}

	// 创建Link对象
	link := &model.Link{
		URL: targetURL,
		// 可以根据需要设置其他参数
		Concurrency: GetIntParam(r, "thread", 4),              // 默认值为4
		PartSize:    GetIntParam(r, "chunkSize", 1024) * 1024, // 默认值为1MB (1024KB)
		Header:      customHeaders,                            // 添加自定义请求头
	}

	// 从URL获取文件信息（名称和大小）
	fileName, fileSize, err := utils.GetFileInfoFromURL(r.Context(), targetURL, customHeaders)
	if err != nil {
		http.Error(w, fmt.Sprintf("failed to get file info: %v", err), http.StatusInternalServerError)
		return
	}

	// 创建文件对象，使用从URL获取的信息
	file := &utils.FileObj{
		Name:     fileName,
		ModTime_: time.Now(),
		FileSize: fileSize,
	}

	// 调用Proxy函数
	if err := common.Proxy(w, r, link, file); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

// HealthHandler 健康检查处理器
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

// GetIntParam 从请求中获取整数参数，如果获取失败则返回默认值
func GetIntParam(r *http.Request, name string, defaultValue int) int {
	value := r.URL.Query().Get(name)
	if value == "" {
		return defaultValue
	}

	intValue := 0
	if _, err := fmt.Sscanf(value, "%d", &intValue); err != nil || intValue <= 0 {
		return defaultValue
	}

	return intValue
}
