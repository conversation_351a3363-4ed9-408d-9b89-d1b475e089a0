import 'package:flutter/material.dart';
import '../models/file_item.dart';

class BreadcrumbNavigation extends StatelessWidget {
  final List<PathItem> path;
  final Function(String cid, String name) onNavigate;

  const BreadcrumbNavigation({
    super.key,
    required this.path,
    required this.onNavigate,
  });

  @override
  Widget build(BuildContext context) {
    if (path.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            Icon(
              Icons.home,
              size: 20,
              color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.grey[300] 
                    : Colors.grey[400],
            ),
            const SizedBox(width: 8),
            Text(
              '根目录',
              style: TextStyle(
                color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.grey[300] 
                      : Colors.grey[400],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: ConstrainedBox(
              constraints: BoxConstraints(minWidth: constraints.maxWidth),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: _buildBreadcrumbItems(context),
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildBreadcrumbItems(BuildContext context) {
    final List<Widget> items = [];

    // 添加根目录
    items.add(
      _BreadcrumbItem(
        icon: Icons.home,
        text: '根目录',
        isLast: path.isEmpty,
        onTap: () => onNavigate('0', '根目录'),
      ),
    );

    // 添加路径项
    for (int i = 0; i < path.length; i++) {
      final pathItem = path[i];
      final isLast = i == path.length - 1;

      // 添加分隔符
      items.add(
        Icon(
          Icons.chevron_right,
          size: 16,
          color: Theme.of(context).brightness == Brightness.dark 
            ? Colors.grey[300] 
            : Colors.grey[400],
        ),
      );

      items.add(
        _BreadcrumbItem(
          text: pathItem.name,
          isLast: isLast,
          onTap: isLast ? null : () => onNavigate(pathItem.id, pathItem.name),
        ),
      );
    }

    return items;
  }
}

class _BreadcrumbItem extends StatelessWidget {
  final IconData? icon;
  final String text;
  final bool isLast;
  final VoidCallback? onTap;

  const _BreadcrumbItem({
    this.icon,
    required this.text,
    required this.isLast,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final textStyle = TextStyle(
      color: isLast 
          ? (theme.brightness == Brightness.dark 
              ? Colors.grey[200] 
              : Colors.grey[600])
          : (theme.brightness == Brightness.dark 
              ? Colors.grey[300] 
              : Colors.grey[400]),
      fontWeight: isLast ? FontWeight.normal : FontWeight.w500,
      fontSize: 14,
    );

    final content = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: 18,
            color: textStyle.color,
          ),
          const SizedBox(width: 4),
        ],
        Flexible(
          child: Text(
            text,
            style: textStyle,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
          child: content,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: content,
    );
  }
} 