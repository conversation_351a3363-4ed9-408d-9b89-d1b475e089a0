package model

import (
	"context"
	"io"
	"net/http"
	"time"

	"go_proxy/pkg/http_range"
	"go_proxy/pkg/utils"
)

type Link struct {
	URL             string            `json:"url"`    // most common way
	Header          http.Header       `json:"header"` // needed header (for url)
	RangeReadCloser RangeReadCloserIF `json:"-"`      // recommended way if can't use URL

	Expiration *time.Duration // local cache expire Duration
	IPCacheKey bool           `json:"-"` // add ip to cache key

	//for accelerating request, use multi-thread downloading
	Concurrency int `json:"concurrency"`
	PartSize    int `json:"part_size"`
}

type RangeReadCloserIF interface {
	RangeRead(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error)
	utils.ClosersIF
}

var _ RangeReadCloserIF = (*RangeReadCloser)(nil)

type RangeReadCloser struct {
	RangeReader RangeReaderFunc
	utils.Closers
}

func (r *RangeReadCloser) RangeRead(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error) {
	rc, err := r.RangeReader(ctx, httpRange)
	r.Closers.Add(rc)
	return rc, err
}

// type WriterFunc func(w io.Writer) error
type RangeReaderFunc func(ctx context.Context, httpRange http_range.Range) (io.ReadCloser, error)
