# Token 验证系统改进指南

## 概述

本文档介绍了改进后的 token 验证系统，该系统提供了更详细的错误信息、更好的用户体验，以及更完善的调试功能。

## 主要改进

### 1. 详细的验证状态 (`TokenValidationResult`)

新的验证系统不再只返回简单的 true/false，而是提供详细的状态信息：

```dart
enum TokenValidationStatus {
  valid,           // Token有效
  noToken,         // 没有Token
  needsRelogin,    // 需要重新登录（refresh token无效/过期）
  refreshFailed,   // 刷新失败
  networkError,    // 网络错误
  unknownError,    // 未知错误
}
```

### 2. 自动 Token 刷新机制

改进后的 `isTokenValid()` 方法现在利用现有的自动刷新机制：

- 如果 access token 过期但 refresh token 仍有效，系统会自动刷新 token
- 只有当 refresh token 也无效时，才会要求用户重新登录
- 所有过程都有详细的日志记录

### 3. 用户友好的错误提示

应用启动时，用户现在会看到具体的错误信息而不是静默跳转：

- **登录已过期**：当 refresh token 无效或过期时
- **网络连接错误**：当无法连接到服务器时（提供重试选项）
- **验证失败**：当遇到未知错误时（提供重试选项）

## 错误排查

### 查看 API 日志

可以通过以下方式查看详细的 API 调用日志：

```dart
final apiService = ApiService();

// 获取所有日志
final allLogs = apiService.getAllLogs();

// 获取错误级别的日志
final errorLogs = apiService.getLogsByLevel({'ERROR'});

// 搜索特定关键词的日志
final tokenLogs = apiService.searchLogs('token');

// 获取最近的日志
final recentLogs = apiService.getRecentLogs(20);
```

### 关键日志标识

在日志中查找以下关键信息：

1. **Token 验证日志**：
   - `VALIDATION /token/check` - Token 验证过程
   - 状态码和错误信息会显示具体的失败原因

2. **Token 刷新日志**：
   - `REFRESH /token/refresh` - Token 刷新过程
   - `[TOKEN_REFRESHED]` 标签表示成功刷新

3. **Token 错误处理日志**：
   - `TOKEN_ERROR` - 自动处理 token 相关错误
   - 包含具体的错误码和处理结果

## 常见问题和解决方案

### 1. "登录已过期" 错误

**原因**：Refresh token 无效或已过期
**解决方案**：
- 检查日志中的具体错误码
- 重新登录获取新的 token

**相关错误码**：
- `40140116` - refresh_token 无效
- `40140119` - refresh_token 已过期

### 2. "网络连接错误"

**原因**：无法连接到 115 服务器
**解决方案**：
- 检查网络连接
- 点击"重试"按钮
- 检查代理设置（如果使用）

### 3. "登录刷新失败"

**原因**：Access token 无效且自动刷新失败
**解决方案**：
- 检查 refresh token 是否有效
- 查看日志中的具体刷新失败原因
- 重新登录

### 4. "Token刷新太频繁"

**原因**：短时间内多次尝试刷新 token
**解决方案**：
- 等待一段时间后重试
- 检查应用是否有异常的频繁请求

**相关错误码**：
- `40140117` - access_token 刷新太频繁

## 调试技巧

### 1. 启用详细日志

```dart
final apiService = ApiService();

// 启用控制台输出
apiService.setLogConsoleOutput(true);

// 启用所有级别的日志
apiService.setLogEnabledLevels({'DEBUG', 'INFO', 'WARNING', 'ERROR'});
```

### 2. 监控 Token 状态

在关键操作前检查 token 状态：

```dart
final validationResult = await apiService.isTokenValid();

switch (validationResult.status) {
  case TokenValidationStatus.valid:
    // 继续操作
    break;
  case TokenValidationStatus.needsRelogin:
    // 引导用户重新登录
    print('需要重新登录: ${validationResult.errorMessage}');
    break;
  case TokenValidationStatus.networkError:
    // 处理网络错误
    print('网络错误: ${validationResult.errorMessage}');
    break;
  // ... 处理其他状态
}
```

### 3. 手动清除无效 Token

如果遇到持续的 token 问题：

```dart
final apiService = ApiService();
await apiService.clearTokens();
// 然后引导用户重新登录
```

## 最佳实践

1. **定期检查**：在应用启动和关键操作前验证 token
2. **优雅降级**：网络错误时提供重试选项，而不是直接跳转到登录页
3. **日志监控**：定期查看 API 日志，及早发现问题
4. **用户提示**：提供清晰的错误信息，告诉用户具体发生了什么

## API 参考

### TokenValidationResult

```dart
class TokenValidationResult {
  final bool isValid;                    // 是否有效
  final TokenValidationStatus status;    // 具体状态
  final String? errorMessage;            // 错误信息
  final int? errorCode;                  // API 错误码
  final bool wasRefreshed;               // 是否进行了刷新
}
```

### 便捷构造函数

```dart
// 成功
TokenValidationResult.success(wasRefreshed: true)

// 需要重新登录
TokenValidationResult.needsRelogin('刷新令牌已过期', errorCode: 40140119)

// 网络错误
TokenValidationResult.networkError('连接超时')
```

---

通过这些改进，你现在可以更容易地诊断和解决 token 相关的问题，提供更好的用户体验。 